/**
 * Redis提供者
 */

import { Provider, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Redis from 'ioredis';

/**
 * Redis连接提供者
 */
export const RedisProvider: Provider = {
  provide: 'REDIS_CLIENT',
  useFactory: async (configService: ConfigService): Promise<Redis> => {
    const logger = new Logger('RedisProvider');

    const redisConfig = {
      host: configService.get<string>('REDIS_HOST', 'localhost'),
      port: configService.get<number>('REDIS_PORT', 6379),
      password: configService.get<string>('REDIS_PASSWORD'),
      db: configService.get<number>('REDIS_DB', 0),
      retryDelayOnFailover: 100,
      enableReadyCheck: true,
      maxRetriesPerRequest: 3,
      lazyConnect: true,
      keepAlive: 30000,
      connectTimeout: 10000,
      commandTimeout: 5000,
      retryDelayOnClusterDown: 300,
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
    };

    const redis = new Redis(redisConfig);

    // 连接事件监听
    redis.on('connect', () => {
      logger.log('Redis连接已建立');
    });

    redis.on('ready', () => {
      logger.log('Redis连接就绪');
    });

    redis.on('error', (error) => {
      logger.error('Redis连接错误:', error);
    });

    redis.on('close', () => {
      logger.warn('Redis连接已关闭');
    });

    redis.on('reconnecting', (time) => {
      logger.log(`Redis正在重连... (${time}ms)`);
    });

    redis.on('end', () => {
      logger.warn('Redis连接已结束');
    });

    try {
      await redis.connect();
      logger.log('Redis连接成功');
    } catch (error) {
      logger.error('Redis连接失败:', error);
      throw error;
    }

    return redis;
  },
  inject: [ConfigService],
};

/**
 * Redis发布订阅提供者
 */
export const RedisPubSubProvider: Provider = {
  provide: 'REDIS_PUBSUB',
  useFactory: async (configService: ConfigService): Promise<{
    publisher: Redis;
    subscriber: Redis;
  }> => {
    const logger = new Logger('RedisPubSubProvider');

    const redisConfig = {
      host: configService.get<string>('REDIS_HOST', 'localhost'),
      port: configService.get<number>('REDIS_PORT', 6379),
      password: configService.get<string>('REDIS_PASSWORD'),
      db: configService.get<number>('REDIS_DB', 0),
      retryDelayOnFailover: 100,
      enableReadyCheck: true,
      maxRetriesPerRequest: 3,
      lazyConnect: true,
    };

    const publisher = new Redis(redisConfig);
    const subscriber = new Redis(redisConfig);

    // 发布者事件监听
    publisher.on('connect', () => {
      logger.log('Redis发布者连接已建立');
    });

    publisher.on('error', (error) => {
      logger.error('Redis发布者连接错误:', error);
    });

    // 订阅者事件监听
    subscriber.on('connect', () => {
      logger.log('Redis订阅者连接已建立');
    });

    subscriber.on('error', (error) => {
      logger.error('Redis订阅者连接错误:', error);
    });

    try {
      await Promise.all([
        publisher.connect(),
        subscriber.connect(),
      ]);
      logger.log('Redis发布订阅连接成功');
    } catch (error) {
      logger.error('Redis发布订阅连接失败:', error);
      throw error;
    }

    return { publisher, subscriber };
  },
  inject: [ConfigService],
};
