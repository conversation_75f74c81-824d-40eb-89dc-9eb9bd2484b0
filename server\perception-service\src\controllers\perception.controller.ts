/**
 * 感知数据处理控制器
 */

import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpException,
  Logger,
  UseGuards,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { EventEmitter2 } from '@nestjs/event-emitter';

import { PerceptionProcessingService } from '../services/perception-processing.service';
import {
  ProcessPerceptionDataDto,
  BatchProcessPerceptionDataDto,
  UpdateConfigDto,
  PerceptionDataResponseDto,
  FusedPerceptionDataResponseDto,
  StatisticsResponseDto,
} from '../dto/perception.dto';

/**
 * 感知数据处理控制器
 */
@ApiTags('perception')
@Controller('perception')
@UsePipes(new ValidationPipe({ transform: true }))
export class PerceptionController {
  private readonly logger = new Logger(PerceptionController.name);

  constructor(
    private readonly perceptionService: PerceptionProcessingService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * 处理单个感知数据
   */
  @Post('process')
  @ApiOperation({ summary: '处理单个感知数据' })
  @ApiBody({ type: ProcessPerceptionDataDto })
  @ApiResponse({
    status: 200,
    description: '数据处理成功',
    type: PerceptionDataResponseDto,
  })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  async processPerceptionData(
    @Body() processDto: ProcessPerceptionDataDto,
  ): Promise<PerceptionDataResponseDto> {
    try {
      this.logger.log(`处理感知数据: ${processDto.id}`);

      await this.perceptionService.processPerceptionData(processDto);

      return {
        success: true,
        message: '感知数据处理成功',
        data: {
          id: processDto.id,
          entityId: processDto.entityId,
          processed: true,
          timestamp: Date.now(),
        },
      };
    } catch (error) {
      this.logger.error(`感知数据处理失败: ${error.message}`, error.stack);
      throw new HttpException(
        {
          success: false,
          message: '感知数据处理失败',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * 批量处理感知数据
   */
  @Post('process/batch')
  @ApiOperation({ summary: '批量处理感知数据' })
  @ApiBody({ type: BatchProcessPerceptionDataDto })
  @ApiResponse({
    status: 200,
    description: '批量处理成功',
  })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  async batchProcessPerceptionData(
    @Body() batchDto: BatchProcessPerceptionDataDto,
  ): Promise<any> {
    try {
      this.logger.log(`批量处理感知数据: ${batchDto.data.length} 条`);

      const results = await Promise.allSettled(
        batchDto.data.map(data => this.perceptionService.processPerceptionData(data))
      );

      const successful = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;

      return {
        success: true,
        message: '批量处理完成',
        data: {
          total: batchDto.data.length,
          successful,
          failed,
          results: results.map((result, index) => ({
            index,
            status: result.status,
            error: result.status === 'rejected' ? result.reason.message : null,
          })),
        },
      };
    } catch (error) {
      this.logger.error(`批量处理失败: ${error.message}`, error.stack);
      throw new HttpException(
        {
          success: false,
          message: '批量处理失败',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * 获取实体感知数据
   */
  @Get('entity/:entityId/data')
  @ApiOperation({ summary: '获取实体感知数据' })
  @ApiParam({ name: 'entityId', description: '实体ID' })
  @ApiQuery({ name: 'limit', required: false, description: '数据条数限制' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: [PerceptionDataResponseDto],
  })
  async getEntityPerceptionData(
    @Param('entityId') entityId: string,
    @Query('limit') limit: number = 10,
  ): Promise<any> {
    try {
      const data = await this.perceptionService.getEntityPerceptionData(
        entityId,
        limit,
      );

      return {
        success: true,
        message: '获取实体感知数据成功',
        data,
      };
    } catch (error) {
      this.logger.error(`获取实体感知数据失败: ${error.message}`, error.stack);
      throw new HttpException(
        {
          success: false,
          message: '获取实体感知数据失败',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * 获取实体融合数据
   */
  @Get('entity/:entityId/fused')
  @ApiOperation({ summary: '获取实体融合数据' })
  @ApiParam({ name: 'entityId', description: '实体ID' })
  @ApiQuery({ name: 'limit', required: false, description: '数据条数限制' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: [FusedPerceptionDataResponseDto],
  })
  async getEntityFusedData(
    @Param('entityId') entityId: string,
    @Query('limit') limit: number = 10,
  ): Promise<any> {
    try {
      const data = await this.perceptionService.getEntityFusedData(
        entityId,
        limit,
      );

      return {
        success: true,
        message: '获取实体融合数据成功',
        data,
      };
    } catch (error) {
      this.logger.error(`获取实体融合数据失败: ${error.message}`, error.stack);
      throw new HttpException(
        {
          success: false,
          message: '获取实体融合数据失败',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * 获取处理统计信息
   */
  @Get('statistics')
  @ApiOperation({ summary: '获取处理统计信息' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: StatisticsResponseDto,
  })
  async getStatistics(): Promise<StatisticsResponseDto> {
    try {
      const statistics = this.perceptionService.getStatistics();

      return {
        success: true,
        message: '获取统计信息成功',
        data: statistics,
      };
    } catch (error) {
      this.logger.error(`获取统计信息失败: ${error.message}`, error.stack);
      throw new HttpException(
        {
          success: false,
          message: '获取统计信息失败',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * 更新处理配置
   */
  @Put('config')
  @ApiOperation({ summary: '更新处理配置' })
  @ApiBody({ type: UpdateConfigDto })
  @ApiResponse({ status: 200, description: '配置更新成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async updateConfig(@Body() configDto: UpdateConfigDto): Promise<any> {
    try {
      this.perceptionService.updateConfig(configDto);

      return {
        success: true,
        message: '配置更新成功',
        data: configDto,
      };
    } catch (error) {
      this.logger.error(`配置更新失败: ${error.message}`, error.stack);
      throw new HttpException(
        {
          success: false,
          message: '配置更新失败',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
